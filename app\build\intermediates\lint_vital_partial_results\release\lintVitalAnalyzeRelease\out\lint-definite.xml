<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.3.0" type="incidents">

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="17773"
                endOffset="17807"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="481"
            column="43"
            startOffset="18386"
            endLine="481"
            endColumn="47"
            endOffset="18390"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="17567"
                endOffset="17601"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="484"
            column="43"
            startOffset="18484"
            endLine="484"
            endColumn="47"
            endOffset="18488"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="18182"
                endOffset="18206"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="487"
            column="40"
            startOffset="18583"
            endLine="487"
            endColumn="44"
            endOffset="18587"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="17979"
                endOffset="18013"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="490"
            column="43"
            startOffset="18681"
            endLine="490"
            endColumn="47"
            endOffset="18685"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="21684"
                endOffset="21708"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="597"
            column="44"
            startOffset="23521"
            endLine="597"
            endColumn="48"
            endOffset="23525"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="21873"
                endOffset="21897"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="601"
            column="44"
            startOffset="23619"
            endLine="601"
            endColumn="48"
            endOffset="23623"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="22065"
                endOffset="22089"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="605"
            column="47"
            startOffset="23723"
            endLine="605"
            endColumn="51"
            endOffset="23727"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="22263"
                endOffset="22287"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="609"
            column="47"
            startOffset="23827"
            endLine="609"
            endColumn="51"
            endOffset="23831"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="22461"
                endOffset="22485"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="613"
            column="47"
            startOffset="23931"
            endLine="613"
            endColumn="51"
            endOffset="23935"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="22659"
                endOffset="22683"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="617"
            column="47"
            startOffset="24035"
            endLine="617"
            endColumn="51"
            endOffset="24039"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="22858"
                endOffset="22897"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="621"
            column="48"
            startOffset="24141"
            endLine="621"
            endColumn="52"
            endOffset="24145"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="23089"
                endOffset="23128"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="625"
            column="48"
            startOffset="24247"
            endLine="625"
            endColumn="52"
            endOffset="24251"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="23313"
                endOffset="23337"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="629"
            column="41"
            startOffset="24339"
            endLine="629"
            endColumn="45"
            endOffset="24343"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="24413"
                endOffset="24442"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="639"
            column="45"
            startOffset="24843"
            endLine="639"
            endColumn="49"
            endOffset="24847"/>
    </incident>

    <incident
        id="NullSafeMutableLiveData"
        severity="fatal"
        message="Cannot set non-nullable LiveData value to `null`">
        <fix-replace
            description="Change `LiveData` type to nullable"
            oldString="_lint_insert_end_"
            replacement="?">
            <range
                file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
                startOffset="24615"
                endOffset="24644"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/manaknight/app/viewmodels/BaasViewModel.kt"
            line="643"
            column="45"
            startOffset="24943"
            endLine="643"
            endColumn="49"
            endOffset="24947"/>
    </incident>

</incidents>
