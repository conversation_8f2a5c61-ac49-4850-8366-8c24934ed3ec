
  package com.manaknight.app.data.local


import android.content.Context
import android.content.SharedPreferences
import com.manaknight.app.extensions.Constants
import org.koin.android.ext.koin.androidContext
import org.koin.dsl.module

val prefModule = module {
    single { AppPreferences(androidContext()) }
}

class AppPreferences(context: Context) {

    private val preferences: SharedPreferences =
        context.getSharedPreferences("prefs", Context.MODE_PRIVATE)

    var isLoggedIn: Boolean
        get() = preferences.getBoolean("isLoggedIn", false)
        set(value) = preferences.edit().putBoolean("isLoggedIn", value).apply()

    private var _token: String? = null

    var fcmToken: String?
        get() = preferences.getString("fcm_token", "")
        set(value) = preferences.edit().putString("fcm_token", value).apply()

    var accessToken: String?
        get() = token()
        set(value) = preferences.edit().putString("token", value).apply()

    private fun token(): String? {
        _token?.let { return it } ?: run {
            _token = preferences.getString("token", null)
            return _token
        }
    }

    private var _refreshToken: String? = null
    private var _fcmToken: String? = null


    var refreshToken: String?
        get() = refreshToken()
        set(value) = preferences.edit().putString("refreshToken", value).apply()

    var email: String?
        get() = preferences.getString("email", null)
        set(value) = preferences.edit().putString("email", value).apply()


    var firstName: String?
        get() = preferences.getString("firstName", null)
        set(value) = preferences.edit().putString("firstName", value).apply()

    var userName: String?
        get() = preferences.getString("userName", null)
        set(value) = preferences.edit().putString("userName", value).apply()

    var photo: String?
        get() = preferences.getString("photo", null)
        set(value) = preferences.edit().putString("photo", value).apply()

    var lastName: String?
        get() = preferences.getString("lastName", null)
        set(value) = preferences.edit().putString("lastName", value).apply()

    var password: String?
        get() = preferences.getString("password", null)
        set(value) = preferences.edit().putString("password", value).apply()

    var companyName: String?
        get() = preferences.getString("companyName", null)
        set(value) = preferences.edit().putString("companyName", value).apply()

    var role: String?
        get() = preferences.getString("role", null)
        set(value) = preferences.edit().putString("role", value).apply()

    var defaultProfitOverhead: Int
        get() = preferences.getInt("defaultProfitOverhead", 0)
        set(value) = preferences.edit().putInt("defaultProfitOverhead", value).apply()

    var hasSeenHomepageExplainer: Boolean
        get() = preferences.getBoolean("hasSeenHomepageExplainer", false)
        set(value) = preferences.edit().putBoolean("hasSeenHomepageExplainer", value).apply()

    // Face ID / Biometric Authentication Settings
    var isFaceIdEnabled: Boolean
        get() = preferences.getBoolean("isFaceIdEnabled", false)
        set(value) = preferences.edit().putBoolean("isFaceIdEnabled", value).apply()

    var faceIdCredentialsStored: Boolean
        get() = preferences.getBoolean("faceIdCredentialsStored", false)
        set(value) = preferences.edit().putBoolean("faceIdCredentialsStored", value).apply()


    fun logout() {
         userId = null
         _token = null
         fcmToken = null
         accessToken = null
         email = null
         firstName = null
         lastName = null
        password = null

        // age = null
         photo = null
        // deviceId = -1
        isLoggedIn = false
        // Note: Face ID settings should persist across logout/login cycles
        // Users shouldn't have to re-enable Face ID every time they logout
        // isAccountSetup = false
        // isNewAccount = true
        // isAllowPush = false
        // profileImage = null
        // profileBlurhash = null
        // myInterestID = null
        // interests = null
        // gender = null
    }

    private fun refreshToken(): String? {
        _refreshToken?.let { return it } ?: run {
            _refreshToken = preferences.getString("refreshToken", null)
            return _refreshToken
        }
    }

    fun saveUser(
        id: Int?,
        token: String?,
        userRole: String?,
        emailAddress: String?,
        userphoto: String?,
        first: String?,
        last: String?,
        password: String?,
        companyName: String?,
    ) {
        userId = id
        accessToken = "Bearer $token"
        Constants.AUTH_TOKEN.value = "Bearer $token"
        role = userRole
        isLoggedIn = true
        photo = userphoto
        email = emailAddress
        firstName = first
        lastName = last
        this.password = password
        this.companyName = companyName
    }

    var userId: Int?
        get() = preferences.getInt("userId", -1)
        set(value) = preferences.edit().putInt("userId", value ?: -1).apply()



    fun saveChatBotResponse(response: String?) {
            chatBotResponse = response
        }

    var chatBotResponse: String?
            get() = preferences.getString("chatBotResponse", "")
            set(value) = preferences.edit().putString("chatBotResponse", value).apply()

}

