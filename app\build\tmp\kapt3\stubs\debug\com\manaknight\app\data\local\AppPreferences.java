package com.manaknight.app.data.local;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u000e\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u001c\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\u0002\n\u0002\b\f\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010J\u001a\u00020KJ\n\u0010<\u001a\u0004\u0018\u00010\u0006H\u0002J\u0010\u0010L\u001a\u00020K2\b\u0010M\u001a\u0004\u0018\u00010\u0006Je\u0010N\u001a\u00020K2\b\u0010O\u001a\u0004\u0018\u00010\u00152\b\u0010P\u001a\u0004\u0018\u00010\u00062\b\u0010Q\u001a\u0004\u0018\u00010\u00062\b\u0010R\u001a\u0004\u0018\u00010\u00062\b\u0010S\u001a\u0004\u0018\u00010\u00062\b\u0010T\u001a\u0004\u0018\u00010\u00062\b\u0010U\u001a\u0004\u0018\u00010\u00062\b\u00104\u001a\u0004\u0018\u00010\u00062\b\u0010\u0012\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0002\u0010VJ\n\u0010P\u001a\u0004\u0018\u00010\u0006H\u0002R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R(\u0010\n\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b\u000b\u0010\f\"\u0004\b\r\u0010\u000eR(\u0010\u000f\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b\u0010\u0010\f\"\u0004\b\u0011\u0010\u000eR(\u0010\u0012\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b\u0013\u0010\f\"\u0004\b\u0014\u0010\u000eR$\u0010\u0016\u001a\u00020\u00152\u0006\u0010\t\u001a\u00020\u00158F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR(\u0010\u001b\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b\u001c\u0010\f\"\u0004\b\u001d\u0010\u000eR$\u0010\u001f\u001a\u00020\u001e2\u0006\u0010\t\u001a\u00020\u001e8F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b \u0010!\"\u0004\b\"\u0010#R(\u0010$\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b%\u0010\f\"\u0004\b&\u0010\u000eR(\u0010\'\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b(\u0010\f\"\u0004\b)\u0010\u000eR$\u0010*\u001a\u00020\u001e2\u0006\u0010\t\u001a\u00020\u001e8F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b+\u0010!\"\u0004\b,\u0010#R$\u0010-\u001a\u00020\u001e2\u0006\u0010\t\u001a\u00020\u001e8F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b-\u0010!\"\u0004\b.\u0010#R$\u0010/\u001a\u00020\u001e2\u0006\u0010\t\u001a\u00020\u001e8F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b/\u0010!\"\u0004\b0\u0010#R(\u00101\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b2\u0010\f\"\u0004\b3\u0010\u000eR(\u00104\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b5\u0010\f\"\u0004\b6\u0010\u000eR(\u00107\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b8\u0010\f\"\u0004\b9\u0010\u000eR\u000e\u0010:\u001a\u00020;X\u0082\u0004\u00a2\u0006\u0002\n\u0000R(\u0010<\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b=\u0010\f\"\u0004\b>\u0010\u000eR(\u0010?\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\b@\u0010\f\"\u0004\bA\u0010\u000eR(\u0010B\u001a\u0004\u0018\u00010\u00152\b\u0010\t\u001a\u0004\u0018\u00010\u00158F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\bC\u0010D\"\u0004\bE\u0010FR(\u0010G\u001a\u0004\u0018\u00010\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00068F@FX\u0086\u000e\u00a2\u0006\f\u001a\u0004\bH\u0010\f\"\u0004\bI\u0010\u000e\u00a8\u0006W"}, d2 = {"Lcom/manaknight/app/data/local/AppPreferences;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_fcmToken", "", "_refreshToken", "_token", "value", "accessToken", "getAccessToken", "()Ljava/lang/String;", "setAccessToken", "(Ljava/lang/String;)V", "chatBotResponse", "getChatBotResponse", "setChatBotResponse", "companyName", "getCompanyName", "setCompanyName", "", "defaultProfitOverhead", "getDefaultProfitOverhead", "()I", "setDefaultProfitOverhead", "(I)V", "email", "getEmail", "setEmail", "", "faceIdCredentialsStored", "getFaceIdCredentialsStored", "()Z", "setFaceIdCredentialsStored", "(Z)V", "fcmToken", "getFcmToken", "setFcmToken", "firstName", "getFirstName", "setFirstName", "hasSeenHomepageExplainer", "getHasSeenHomepageExplainer", "setHasSeenHomepageExplainer", "isFaceIdEnabled", "setFaceIdEnabled", "isLoggedIn", "setLoggedIn", "lastName", "getLastName", "setLastName", "password", "getPassword", "setPassword", "photo", "getPhoto", "setPhoto", "preferences", "Landroid/content/SharedPreferences;", "refreshToken", "getRefreshToken", "setRefreshToken", "role", "getRole", "setRole", "userId", "getUserId", "()Ljava/lang/Integer;", "setUserId", "(Ljava/lang/Integer;)V", "userName", "getUserName", "setUserName", "logout", "", "saveChatBotResponse", "response", "saveUser", "id", "token", "userRole", "emailAddress", "userphoto", "first", "last", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "app_debug"})
public final class AppPreferences {
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences preferences = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String _token;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String _refreshToken;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String _fcmToken;
    
    public AppPreferences(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    public final boolean isLoggedIn() {
        return false;
    }
    
    public final void setLoggedIn(boolean value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFcmToken() {
        return null;
    }
    
    public final void setFcmToken(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getAccessToken() {
        return null;
    }
    
    public final void setAccessToken(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    private final java.lang.String token() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRefreshToken() {
        return null;
    }
    
    public final void setRefreshToken(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getEmail() {
        return null;
    }
    
    public final void setEmail(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFirstName() {
        return null;
    }
    
    public final void setFirstName(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUserName() {
        return null;
    }
    
    public final void setUserName(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPhoto() {
        return null;
    }
    
    public final void setPhoto(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getLastName() {
        return null;
    }
    
    public final void setLastName(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPassword() {
        return null;
    }
    
    public final void setPassword(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCompanyName() {
        return null;
    }
    
    public final void setCompanyName(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRole() {
        return null;
    }
    
    public final void setRole(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    public final int getDefaultProfitOverhead() {
        return 0;
    }
    
    public final void setDefaultProfitOverhead(int value) {
    }
    
    public final boolean getHasSeenHomepageExplainer() {
        return false;
    }
    
    public final void setHasSeenHomepageExplainer(boolean value) {
    }
    
    public final boolean isFaceIdEnabled() {
        return false;
    }
    
    public final void setFaceIdEnabled(boolean value) {
    }
    
    public final boolean getFaceIdCredentialsStored() {
        return false;
    }
    
    public final void setFaceIdCredentialsStored(boolean value) {
    }
    
    public final void logout() {
    }
    
    private final java.lang.String refreshToken() {
        return null;
    }
    
    public final void saveUser(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String token, @org.jetbrains.annotations.Nullable()
    java.lang.String userRole, @org.jetbrains.annotations.Nullable()
    java.lang.String emailAddress, @org.jetbrains.annotations.Nullable()
    java.lang.String userphoto, @org.jetbrains.annotations.Nullable()
    java.lang.String first, @org.jetbrains.annotations.Nullable()
    java.lang.String last, @org.jetbrains.annotations.Nullable()
    java.lang.String password, @org.jetbrains.annotations.Nullable()
    java.lang.String companyName) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getUserId() {
        return null;
    }
    
    public final void setUserId(@org.jetbrains.annotations.Nullable()
    java.lang.Integer value) {
    }
    
    public final void saveChatBotResponse(@org.jetbrains.annotations.Nullable()
    java.lang.String response) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getChatBotResponse() {
        return null;
    }
    
    public final void setChatBotResponse(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
}