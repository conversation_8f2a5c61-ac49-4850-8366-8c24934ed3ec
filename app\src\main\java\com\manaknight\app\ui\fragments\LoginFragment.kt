
    package com.manaknight.app.ui.fragments

    import android.os.Bundle
    import android.view.View
    import androidx.core.widget.doAfterTextChanged
    import androidx.fragment.app.Fragment
    import androidx.navigation.fragment.findNavController
    import Manaknight.R
    import com.manaknight.app.data.local.AppPreferences
    import Manaknight.databinding.FragmentLoginBinding
    import android.app.Dialog
    import com.manaknight.app.extensions.disableSpaces
    import com.manaknight.app.extensions.hideSoftKeyboard
    import com.manaknight.app.extensions.setOnClickWithDebounce
    import com.manaknight.app.extensions.snackBar
    import com.manaknight.app.extensions.viewBinding
    import com.manaknight.app.network.Status
    import com.manaknight.app.viewmodels.BaasViewModel
    import com.manaknight.app.utils.BiometricAuthManager
    import com.manaknight.app.utils.BiometricAuthCallback
    import com.manaknight.app.utils.BiometricAvailability
    import org.koin.android.ext.android.inject
    import androidx.appcompat.app.AppCompatActivity
    import com.manaknight.app.extensions.hide
    import com.manaknight.app.extensions.hideProgressBar
    import com.manaknight.app.extensions.invisible
    import com.manaknight.app.extensions.isEmailValid
    import com.manaknight.app.extensions.showProgressBar
    import com.manaknight.app.utils.ProgressDialog
    import com.manaknight.app.utils.ProgressDialog.Companion.progressDialog
    import org.koin.androidx.viewmodel.ext.android.viewModel

    class LoginFragment : Fragment(R.layout.fragment_login) {

        private val binding by viewBinding(FragmentLoginBinding::bind)
        private val baasViewModel: BaasViewModel by viewModel()
        private val pref by inject<AppPreferences>()
        private var email = ""
        private var password = ""
        private lateinit var dialog: Dialog
        private lateinit var biometricAuthManager: BiometricAuthManager


        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
            super.onViewCreated(view, savedInstanceState)

            dialog = progressDialog(requireContext())
            biometricAuthManager = BiometricAuthManager(requireContext(), pref)
            binding.headerInclude.addPlaterTitle.text = "Sign In"

            setupTextWatchers()
            setupFaceIdButton()

            binding.btnLogin.setOnClickWithDebounce {
                if (email.isNotEmpty() && password.isNotEmpty()) {
                    if (binding.edTxtUserName.isEmailValid()) {
                        requireActivity().hideSoftKeyboard()
                        login(email, password)
                        //findNavController().navigate(R.id.action_loginFragment_to_subscriptionFragment)
                    } else {
                        snackBar("Please enter a valid email address.")
                    }


                }
                else snackBar("Please fill in both the email and password fields.")
            }

            binding.btnFaceId?.setOnClickWithDebounce {
                authenticateWithFaceId()
            }

            binding.btnSignup.setOnClickWithDebounce {
                findNavController().navigate(R.id.action_loginFragment_to_signUpFragment)
            }

            binding.btnForgetPassword.setOnClickWithDebounce {
                findNavController().navigate(R.id.action_loginFragment_to_forgetPasswordFragment)
            }

            binding.edTxtUserName.doAfterTextChanged { email = it.toString() }
            binding.edTxtPassword.doAfterTextChanged { password = it.toString() }
            binding.edTxtUserName.disableSpaces()
            binding.edTxtPassword.disableSpaces()

            binding.constraint.setOnClickListener() {
                requireActivity().hideSoftKeyboard()
            }

            binding.edTxtUserName.setText("<EMAIL>")
            binding.edTxtPassword.setText("Test@123")
            email = "<EMAIL>"
            password = "Test@123"

        }

        private fun setupTextWatchers() {
            binding.edTxtUserName.doAfterTextChanged { email = it.toString() }
            binding.edTxtUserName.disableSpaces()

            binding.edTxtPassword.doAfterTextChanged { password = it.toString() }
            binding.edTxtPassword.disableSpaces()
        }

        private fun setupFaceIdButton() {
            // Safety check - don't proceed if view is not available
            if (!isAdded || view == null) {
                android.util.Log.d("FaceID", "Fragment not added or view is null, skipping button setup")
                return
            }

            val biometricAvailability = biometricAuthManager.isBiometricAvailable()
            val isFaceIdEnabled = biometricAuthManager.isFaceIdEnabled()

            // Debug logging
            android.util.Log.d("FaceID", "Biometric availability: $biometricAvailability")
            android.util.Log.d("FaceID", "Face ID enabled: $isFaceIdEnabled")

            when (biometricAvailability) {
                BiometricAvailability.AVAILABLE -> {
                    // Show Face ID button if biometric is available and user has enabled it
                    if (isFaceIdEnabled) {
                        binding.btnFaceId?.visibility = View.VISIBLE
                        android.util.Log.d("FaceID", "✅ Showing Face ID button - Available & Enabled")
                    } else {
                        binding.btnFaceId?.visibility = View.GONE
                        android.util.Log.d("FaceID", "❌ Hiding Face ID button - Available but not enabled")
                    }
                }
                BiometricAvailability.NONE_ENROLLED -> {
                    // Show Face ID button even if none enrolled, but handle in authentication
                    if (isFaceIdEnabled) {
                        binding.btnFaceId?.visibility = View.VISIBLE
                        android.util.Log.d("FaceID", "✅ Showing Face ID button - None enrolled but enabled")
                    } else {
                        binding.btnFaceId?.visibility = View.GONE
                        android.util.Log.d("FaceID", "❌ Hiding Face ID button - None enrolled and not enabled")
                    }
                }
                else -> {
                    // Hide Face ID button for other cases (no hardware, etc.)
                    binding.btnFaceId?.visibility = View.GONE
                    android.util.Log.d("FaceID", "❌ Hiding Face ID button - Not available: $biometricAvailability")
                }
            }

            // Additional debug info
            android.util.Log.d("FaceID", "Button visibility set to: ${if (binding.btnFaceId?.visibility == View.VISIBLE) "VISIBLE" else "GONE"}")
            android.util.Log.d("FaceID", "Stored preferences - isFaceIdEnabled: ${pref.isFaceIdEnabled}, faceIdCredentialsStored: ${pref.faceIdCredentialsStored}")
        }

        private fun authenticateWithFaceId() {
            biometricAuthManager.authenticateWithFaceId(this, object : BiometricAuthCallback {
                override fun onAuthenticationSucceeded() {
                    // Face ID authentication successful - perform silent login
                    android.util.Log.d("FaceID", "Face ID authentication succeeded, attempting silent login")
                    performSilentLogin()
                }

                override fun onAuthenticationError(errorCode: Int, errorMessage: String) {
                    when (errorCode) {
                        androidx.biometric.BiometricPrompt.ERROR_USER_CANCELED,
                        androidx.biometric.BiometricPrompt.ERROR_NEGATIVE_BUTTON -> {
                            // User canceled or chose to use password
                            snackBar("Face ID canceled. Please use your password to sign in.")
                        }
                        androidx.biometric.BiometricPrompt.ERROR_NO_BIOMETRICS -> {
                            snackBar("No face enrolled. Please set up Face ID in device settings.")
                        }
                        else -> {
                            snackBar("Face ID authentication failed: $errorMessage")
                        }
                    }
                }

                override fun onAuthenticationFailed() {
                    snackBar("Face not recognized. Please try again or use your password.")
                }
            })
        }

        private fun performSilentLogin() {
            // Get stored credentials for silent login
            val storedEmail = pref.email
            val storedPassword = pref.password

            android.util.Log.d("FaceID", "Silent login attempt - Email: ${storedEmail?.take(3)}***, Password: ${if (storedPassword.isNullOrEmpty()) "empty" else "available"}")

            if (!storedEmail.isNullOrEmpty() && !storedPassword.isNullOrEmpty()) {
                android.util.Log.d("FaceID", "Performing silent login with stored credentials")
                login(storedEmail, storedPassword)
            } else {
                android.util.Log.d("FaceID", "No stored credentials available for silent login")
                snackBar("Please sign in with your password first to enable Face ID.")
                biometricAuthManager.disableFaceId()
                binding.btnFaceId?.visibility = View.GONE
            }
        }

        private fun offerFaceIdSetupAndNavigate() {
            // Only offer Face ID setup if it's available and not already enabled
            val biometricAvailability = biometricAuthManager.isBiometricAvailable()

            if (biometricAvailability == BiometricAvailability.AVAILABLE && !biometricAuthManager.isFaceIdEnabled()) {
                // Show dialog to enable Face ID
                android.app.AlertDialog.Builder(requireContext())
                    .setTitle("Enable Face ID")
                    .setMessage("Would you like to use Face ID to sign in quickly next time?")
                    .setPositiveButton("Enable") { _, _ ->
                        biometricAuthManager.enableFaceId()
                        pref.faceIdCredentialsStored = true
                        android.util.Log.d("FaceID", "Face ID enabled by user")
                        // Navigate to home after enabling Face ID
                        navigateToHome()
                    }
                    .setNegativeButton("Not Now") { _, _ ->
                        // Navigate to home even if user declines Face ID
                        navigateToHome()
                    }
                    .setCancelable(false) // Prevent dismissing without choice
                    .show()
            } else {
                // No Face ID setup needed, navigate directly
                navigateToHome()
            }
        }

        private fun navigateToHome() {
            if (isAdded && view != null) {
                findNavController().navigate(R.id.action_loginFragment_to_homeFragment)
            }
        }


        private fun login(email: String, password: String, role: String = "company") {
            android.util.Log.d("FaceID", "Login method called with email: ${email.take(3)}***, role: $role")
            requireActivity().hideSoftKeyboard()
            baasViewModel.loginLambda(email, password, role, true).observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        //snackBar(it.message ?: getString(R.string.something_went_wrong))
                        snackBar("Oops! The email or password you entered is incorrect. Please try again.")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        //snackBar("Logging in...")
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        val data = it.data
                        pref.saveUser(
                            data?.user_id?.toInt(),
                            data?.token,
                            data?.role,
                            email,
                            data?.photo,
                            data?.first_name,
                            data?.last_name,
                            password,
                            ""
                        )

                        // Debug: Check if credentials were saved
                        android.util.Log.d("FaceID", "Credentials saved - Email: ${pref.email?.take(3)}***, Password: ${if (pref.password.isNullOrEmpty()) "empty" else "saved"}")

                        // Offer Face ID setup after successful login, then navigate
                        offerFaceIdSetupAndNavigate()

                        //findNavController().navigate(R.id.action_loginFragment_to_subscriptionFragment)
                    }
                }
            }
        }

          override fun onResume() {
            super.onResume()
            (activity as AppCompatActivity?)?.supportActionBar?.hide()
            binding.headerInclude.backButton.invisible()

            // Refresh Face ID button visibility when returning to login screen
            if (::biometricAuthManager.isInitialized && isAdded && view != null) {
                setupFaceIdButton()
            }
        }

        override fun onStop() {
            super.onStop()
            (activity as AppCompatActivity?)?.supportActionBar?.show()
        }


    }
